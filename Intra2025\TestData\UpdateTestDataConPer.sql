-- 更新測試資料中的 ConPer 欄位，從 "承辦人員" 改為實際的 SSO 帳號
-- 這個腳本用於修正權限控制問題

-- 更新現有的測試資料，將 ConPer 從 "User" 改為 "testuser"（開發環境的預設測試帳號）
UPDATE RemitedList
SET ConPer = 'testuser'
WHERE ConPer = 'User';

-- 檢查更新結果
SELECT 'Updated ConPer field for test data' as Message;
SELECT ConSno, ConPer, COUNT(*) as RecordCount
FROM RemitedList
WHERE ConSno IN (1001, 1002)
GROUP BY ConSno, ConPer;

-- 顯示所有測試資料的 ConPer 欄位值
SELECT ConSno, ConPer, CollecName, RemitPrice
FROM RemitedList
WHERE ConSno IN (1001, 1002)
ORDER BY ConSno, Sno;
