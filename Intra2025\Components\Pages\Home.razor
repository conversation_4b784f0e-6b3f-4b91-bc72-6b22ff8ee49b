@page "/"
@using Intra2025.Components.Base
@inherits BasePageComponent

<PageTitle>支付作業登打系統</PageTitle>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="text-center mb-4">
                <h1 class="display-4 text-primary">支付作業登打系統</h1>
                <p class="lead">歡迎使用支付作業登打系統</p>
                <p class="text-muted">請使用上方導航選單選擇您要使用的功能</p>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-user-edit fa-3x text-primary"></i>
                            </div>
                            <h5 class="card-title">匯款資料維護</h5>
                            <p class="card-text">管理收款人資料和匯款資料</p>
                            <span class="badge bg-info">Step 1</span>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-tasks fa-3x text-success"></i>
                            </div>
                            <h5 class="card-title">匯款整彙作業</h5>
                            <p class="card-text">建立和管理匯款資料</p>
                            <span class="badge bg-success">Step 2</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-body text-center">
                            <div class="mb-3">
                                <i class="fas fa-list-alt fa-3x text-warning"></i>
                            </div>
                            <h5 class="card-title">已產彙清單</h5>
                            <p class="card-text">查詢和匯出匯款記錄</p>
                            <span class="badge bg-warning">Step 3</span>
                        </div>
                    </div>
                </div>
                @if (_userState.IsAdmin)
                {
                    <div class="col-md-6 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-body text-center">
                                <div class="mb-3">
                                    <i class="fas fa-users-cog fa-3x text-danger"></i>
                                </div>
                                <h5 class="card-title">管理者清單</h5>
                                <p class="card-text">管理者作業(匯出清單)</p>
                                <span class="badge bg-danger">Admin</span>
                            </div>
                        </div>
                    </div>
                }
            </div>
            
            <div class="text-center mt-4">
                <div class="alert alert-info">
                    <h6 class="alert-heading">使用說明</h6>
                    <p class="mb-0">
                        請依照 Step 1 → Step 2 → Step 3 的順序進行操作，確保資料處理的完整性。
                        管理者功能僅限具有管理權限的使用者使用。
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    protected override async Task OnInitializedAsync()
    {
        // SSO 驗證已在 BasePageComponent 中自動執行
        await base.OnInitializedAsync();
    }
}
