/* _content/Intra2025/Components/Layout/MainLayout.razor.rz.scp.css */
.page[b-0chhhp7f1x] {
    position: relative;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.top-header[b-0chhhp7f1x] {
    position: sticky;
    top: 0;
    z-index: 1000;
}

.content[b-0chhhp7f1x] {
    flex: 1;
    padding: 20px;
}

.navbar-brand[b-0chhhp7f1x] {
    font-weight: bold;
    font-size: 1.25rem;
}

.nav-link[b-0chhhp7f1x] {
    color: rgba(255, 255, 255, 0.85) !important;
    font-weight: 500;
    padding: 0.5rem 1rem !important;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.nav-link:hover[b-0chhhp7f1x] {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.1);
}

.nav-link:focus[b-0chhhp7f1x] {
    color: white !important;
    background-color: rgba(255, 255, 255, 0.2);
}

.navbar-text .badge[b-0chhhp7f1x] {
    font-size: 0.875rem;
}

@media (max-width: 991.98px) {
    .navbar-text[b-0chhhp7f1x] {
        order: -1;
        margin-right: 0 !important;
        margin-bottom: 0.5rem;
    }

    .navbar-collapse[b-0chhhp7f1x] {
        margin-top: 0.5rem;
    }
}

#blazor-error-ui[b-0chhhp7f1x] {
    background: lightyellow;
    bottom: 0;
    box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
    display: none;
    left: 0;
    padding: 0.6rem 1.25rem 0.7rem 1.25rem;
    position: fixed;
    width: 100%;
    z-index: 1000;
}

#blazor-error-ui .dismiss[b-0chhhp7f1x] {
    cursor: pointer;
    position: absolute;
    right: 0.75rem;
    top: 0.5rem;
}
