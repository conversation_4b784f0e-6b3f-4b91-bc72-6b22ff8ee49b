﻿using System.Net;
using System.Net.Http;
using System.Net.Mail;
using System.Text.Json;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Intra2025.Components.Base
{
    public class EmailService
    {
        private readonly HttpClient _httpClient;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ILogger<EmailService> _logger;
        private readonly NavigationManager _navigationManager;
        private SmtpSettings? _smtpSettings;

        public EmailService(HttpClient httpClient, IHttpClientFactory httpClientFactory, ILogger<EmailService> logger,
        NavigationManager navigationManager)
        {
            _httpClient = httpClient;
            _httpClientFactory = httpClientFactory ?? throw new ArgumentNullException(nameof(httpClientFactory));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _navigationManager = navigationManager ?? throw new ArgumentNullException(nameof(navigationManager));
        }

        // 初始化時載入 SMTP 設定
        public async Task InitializeAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("data/SMTP.json");
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                _smtpSettings = JsonSerializer.Deserialize<SmtpSettings>(json);

                _logger.LogInformation("SMTP 設定載入成功");
            }
            catch (Exception ex)
            {
                _logger.LogError($"無法載入 SMTP 設定: {ex.Message}");
                throw;
            }
        }

        // 發送郵件
        public async Task SendEmailAsync(IEnumerable<string> toEmails, string subject, string body)
        {
            var handler = new HttpClientHandler
            {
                ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true // 忽略 SSL 憑證錯誤
            };

            var httpClient = new HttpClient(handler);
            try
            {
                var baseAddress = new Uri(_navigationManager.BaseUri);
                var response = await httpClient.GetAsync(new Uri(baseAddress, "data/SMTP.json"));
                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                _smtpSettings = JsonSerializer.Deserialize<SmtpSettings>(json);

                _logger.LogInformation("SMTP 設定載入成功");

                if (_smtpSettings == null)
                {
                    throw new InvalidOperationException("SMTP 設定尚未初始化");
                }

                var smtpClient = new SmtpClient(_smtpSettings.Host)
                {
                    Port = _smtpSettings.Port,
                    UseDefaultCredentials = true // 使用預設憑證
                };

                var mailMessage = new MailMessage
                {
                    From = new MailAddress(_smtpSettings.FromEmail ?? "<EMAIL>"),
                    Subject = subject,
                    Body = body,
                    IsBodyHtml = true
                };

                // 將每個收件人加入收件人清單
                foreach (var toEmail in toEmails)
                {
                    mailMessage.To.Add(toEmail);
                }

                await smtpClient.SendMailAsync(mailMessage);
                _logger.LogInformation("郵件發送成功");
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError($"HTTP 請求錯誤：{ex.Message}", ex);
                throw;
            }
            catch (Exception ex)
            {
                _logger.LogError($"郵件發送時發生錯誤：{ex.Message}", ex);
                throw;
            }
        }


        private class SmtpSettings
        {
            public string? Host { get; set; }
            public int Port { get; set; }
            public string? FromEmail { get; set; }
        }

        // 從 wwwroot/data/EmailList.json 載入收件人清單
        public async Task<List<string>> LoadEmailListAsync()
        {
            try
            {
                var httpClient = _httpClientFactory.CreateClient();
                var baseAddress = new Uri(_navigationManager.BaseUri);
                var response = await httpClient.GetAsync(new Uri(baseAddress, "data/EmailList.json"));

                response.EnsureSuccessStatusCode();

                var json = await response.Content.ReadAsStringAsync();
                _logger.LogInformation($"載入的 JSON: {json}");

                var emailList = JsonSerializer.Deserialize<List<EmailEntry>>(json, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });

                if (emailList == null)
                {
                    _logger.LogError("EmailList.json 的資料為 null 或無效");
                    return new List<string>();
                }

                // 過濾出有效的電子郵件地址
                var validEmails = emailList
                    .Where(e => !string.IsNullOrWhiteSpace(e.Email))
                    .Select(e => e.Email!)
                    .ToList();

                return validEmails;
            }
            catch (Exception ex)
            {
                _logger.LogError($"無法載入收件人清單: {ex.Message}");
                throw;
            }
        }

        // 定義 EmailEntry 類別來對應 JSON 結構
        public class EmailEntry
        {
            public string? Orga { get; set; }
            public string? Name { get; set; }
            public string? Email { get; set; }
            public string? JobInf { get; set; }
            public string? Memo { get; set; }
        }
    }
}