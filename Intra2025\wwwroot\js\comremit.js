// ComRemit 相關的 JavaScript 函數

// 下載檔案函數
window.downloadFile = function (filename, base64Data) {
    try {
        // 將 base64 轉換為 blob
        const byteCharacters = atob(base64Data);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
            byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: 'application/pdf' });

        // 創建下載連結
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = filename;
        
        // 觸發下載
        document.body.appendChild(link);
        link.click();
        
        // 清理
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
    } catch (error) {
        console.error('下載檔案時發生錯誤:', error);
        alert('下載檔案時發生錯誤: ' + error.message);
    }
};

// 複製文字到剪貼簿
window.copyToClipboard = function (text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            // 使用現代 Clipboard API
            return navigator.clipboard.writeText(text).then(() => {
                return true;
            }).catch((error) => {
                console.error('複製到剪貼簿失敗:', error);
                return false;
            });
        } else {
            // 使用舊方法作為備用
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                const successful = document.execCommand('copy');
                document.body.removeChild(textArea);
                return successful;
            } catch (error) {
                document.body.removeChild(textArea);
                console.error('複製到剪貼簿失敗:', error);
                return false;
            }
        }
    } catch (error) {
        console.error('複製到剪貼簿時發生錯誤:', error);
        return false;
    }
};

// 格式化數字 (加入千分位逗號)
window.formatNumber = function (number) {
    if (number == null || number === '') return '';
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// 驗證身分證字號
window.validateIdNumber = function (id) {
    if (!id || id.length !== 10) return false;
    
    const firstChar = id.charAt(0).toUpperCase();
    const letterMap = {
        'A': 10, 'B': 11, 'C': 12, 'D': 13, 'E': 14, 'F': 15, 'G': 16, 'H': 17,
        'I': 34, 'J': 18, 'K': 19, 'L': 20, 'M': 21, 'N': 22, 'O': 35, 'P': 23,
        'Q': 24, 'R': 25, 'S': 26, 'T': 27, 'U': 28, 'V': 29, 'W': 32, 'X': 30,
        'Y': 31, 'Z': 33
    };
    
    if (!letterMap[firstChar]) return false;
    
    const letterValue = letterMap[firstChar];
    let sum = Math.floor(letterValue / 10) + (letterValue % 10) * 9;
    
    for (let i = 1; i < 9; i++) {
        const digit = parseInt(id.charAt(i));
        if (isNaN(digit)) return false;
        sum += digit * (9 - i);
    }
    
    const checkDigit = parseInt(id.charAt(9));
    if (isNaN(checkDigit)) return false;
    
    return (sum + checkDigit) % 10 === 0;
};

// 顯示載入中遮罩
window.showLoading = function (message = '載入中...') {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
    `;
    
    const content = document.createElement('div');
    content.style.cssText = `
        background: white;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    `;
    
    content.innerHTML = `
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">載入中...</span>
        </div>
        <div class="mt-2">${message}</div>
    `;
    
    overlay.appendChild(content);
    document.body.appendChild(overlay);
};

// 隱藏載入中遮罩
window.hideLoading = function () {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        document.body.removeChild(overlay);
    }
};

// 確認對話框
window.confirmDialog = function (message, title = '確認') {
    return new Promise((resolve) => {
        if (window.confirm(message)) {
            resolve(true);
        } else {
            resolve(false);
        }
    });
};

// 警告對話框
window.alertDialog = function (message, title = '提示') {
    return new Promise((resolve) => {
        window.alert(message);
        resolve();
    });
};
