﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Hosting;
using static Intra2025.Components.Base.JsonDataLoader;

namespace Intra2025.Components.Base
{
    public class JsonDataLoader
    {

        private readonly IWebHostEnvironment env;

        // Constructor to inject IWebHostEnvironment
        public JsonDataLoader(IWebHostEnvironment env)
        {
            this.env = env;
        }

        // Load categories from JSON file
        public List<Category> LoadCategoriesFromJson(string kind)  //傳入kind(類別)=> A: 兒少關懷服務通報 B:溫馨關懷表
        {
            var filePath = Path.Combine(env.WebRootPath, "data", "ChildCareKind.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = File.ReadAllText(filePath);
            var categories = JsonSerializer.Deserialize<List<Category>>(jsonString);

            if (categories == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            var filteredCaseBelongs = categories.Where(cb => cb.Id?.StartsWith(kind) == true).ToList();

            return filteredCaseBelongs;
        }

        public async Task<List<Category>> LoadCategoriesFromJsonAsync(string kind)  //傳入kind(類別)=> A: 兒少關懷服務通報 B:溫馨關懷表
        {
            var filePath = Path.Combine(env.WebRootPath, "data", "ChildCareKind.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = await File.ReadAllTextAsync(filePath);
            var categories = JsonSerializer.Deserialize<List<Category>>(jsonString);

            if (categories == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            var filteredCaseBelongs = categories.Where(cb => cb.Id?.StartsWith(kind) == true).ToList();

            return filteredCaseBelongs;
        }

        public async Task<List<CaseBelong>> LoadCaseBelongFromJsonAsync()
        {
            var filePath = Path.Combine(env.WebRootPath, "data", "CaseBelong.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = await File.ReadAllTextAsync(filePath);
            var caseBelongs = JsonSerializer.Deserialize<List<CaseBelong>>(jsonString);

            if (caseBelongs == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            return caseBelongs;
        } //讀取12個戶所資料

        public List<CaseBelong> LoadCaseBelongFromJson()
        {
            var filePath = Path.Combine(env.WebRootPath, "data", "CaseBelong.json");

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException("The JSON file was not found.", filePath);
            }

            var jsonString = File.ReadAllText(filePath);
            var caseBelongs = JsonSerializer.Deserialize<List<CaseBelong>>(jsonString);

            if (caseBelongs == null)
            {
                throw new InvalidDataException("Failed to deserialize the JSON data into a list of categories.");
            }

            return caseBelongs;
        }  //讀取12個戶所資料

        public async Task<List<string>> LoadRecipientsFromJsonAsync()
        {
            // JSON 檔案路徑
            var jsonFilePath = Path.Combine(env.WebRootPath, "data", "EmailList.json");

            if (!File.Exists(jsonFilePath))
            {
                throw new FileNotFoundException($"檔案未找到：{jsonFilePath}");
            }

            var jsonContent = await File.ReadAllTextAsync(jsonFilePath);
            var emailEntries = JsonSerializer.Deserialize<List<Recipient>>(jsonContent);

            // 過濾符合類別的有效電子郵件
            var recipients = new List<string>();
            if (emailEntries != null)
            {
                foreach (var entry in emailEntries)
                {
                    if (!string.IsNullOrEmpty(entry.Email))
                    {
                        recipients.Add(entry.Email);
                    }
                }
            }
            return recipients;
        } //讀取【守護寶貝即時通】要通知發送的MAIL清單

        public async Task<List<string>> GetAdminAccountsAsync()
        {
            // JSON 檔案路徑
            var jsonFilePath = Path.Combine(env.WebRootPath, "restrictData", "sysconfig.json");
            try
            {
                // 非同步讀取 JSON 文件
                var json = await File.ReadAllTextAsync(jsonFilePath);

                // 解析 JSON
                var data = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(json);
                return data?["AdminAccounts"] ?? new List<string>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"讀取帳號列表時發生錯誤: {ex.Message}");
                return new List<string>();
            }
        } //讀取【管理者】的清單

        public List<string> GetAdminAccounts()
        {
            // JSON 檔案路徑
            var jsonFilePath = Path.Combine(env.WebRootPath, "restrictData", "sysconfig.json");
            try
            {
                // 非同步讀取 JSON 文件
                var json = File.ReadAllText(jsonFilePath);

                // 解析 JSON
                var data = JsonSerializer.Deserialize<Dictionary<string, List<string>>>(json);
                return data?["AdminAccounts"] ?? new List<string>();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"讀取帳號列表時發生錯誤: {ex.Message}");
                return new List<string>();
            }
        } //讀取【管理者】的清單

        public class Category
        {
            [JsonPropertyName("id")]
            public string? Id { get; set; }

            [JsonPropertyName("name")]
            public string? Name { get; set; }
        } //定義【個案類型】

        public class CaseBelong
        {
            [JsonPropertyName("id")]
            public string? Id { get; set; }

            [JsonPropertyName("name")]
            public string? Name { get; set; }
        } //定義【所屬戶所】

        public class Recipient
        {
            [JsonPropertyName("orga")]
            public string? Orga { get; set; }
            [JsonPropertyName("name")]
            public string? Name { get; set; }
            [JsonPropertyName("email")]
            public string? Email { get; set; }
            [JsonPropertyName("JobInf")]
            public string? JobInf { get; set; }
            [JsonPropertyName("memo")]
            public string? Memo { get; set; }
        } //定義【Mail】的資料格式
    }
}
