@page "/ComRemit/PayeeMt"
@page "/ComRemit/PayeeMt/{EncodedParams}"
@rendermode @(new InteractiveServerRenderMode(prerender: false))
@inject ComRemitPayeeService PayeeService
@inject IJSRuntime JSRuntime
@using Intra2025.Servervices
@using Intra2025.Models.ComRemit
@using Intra2025.Components.Base
@using Microsoft.AspNetCore.Components.Web
@using System.Web
@inherits BasePageComponent

<PageTitle>@(isEditMode ? "編輯收款人資料" : "新增收款人資料")</PageTitle>

<!-- 簡化的樣式覆蓋 -->
<style>
    .payee-mt-page {
        width: 100%;
        margin: 0;
        padding: 20px;
        box-sizing: border-box;
    }

    .form-container {
        max-width: 800px;
        margin: 0 auto;
    }

    .required-field::after {
        content: " *";
        color: red;
    }
</style>

<!-- 使用簡潔的容器 -->
<div class="payee-mt-page">
    <!-- 使用者資訊區塊 -->
    <div class="mb-3 d-flex justify-content-between align-items-center">
        <div>
            <span class="badge text-white" style="background-color:#127681; border-radius: 10px;font-size:14px">
                【@_userState.UnitName-@_userState.UserName】
            </span>
            <span class="text-primary fw-bold ms-2">支付作業登打系統</span>
        </div>
    </div>

    <div class="card form-container">
        <div class="card-header">
            <h4>@(isEditMode ? "【編輯收款人資料】" : "【新增收款人資料】")</h4>
        </div>
        <div class="card-body" style="padding: 30px;">
            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    @errorMessage
                </div>
            }

            @if (!string.IsNullOrEmpty(successMessage))
            {
                <div class="alert alert-success">
                    @successMessage
                    @if (shouldNavigateToList)
                    {
                        <div class="mt-2">
                            <button class="btn btn-primary" @onclick="NavigateToList">返回收款人清單</button>
                        </div>
                    }
                </div>
            }

            @if (isLoading)
            {
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">載入中...</span>
                    </div>
                    <p>載入中...</p>
                </div>
            }
            else
            {
                <EditForm Model="@payee" OnValidSubmit="HandleValidSubmit">
                    <DataAnnotationsValidator />
                    <ValidationSummary class="alert alert-danger" />

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="collectName" class="form-label required-field">收款人姓名</label>
                            <InputText id="collectName" class="form-control" @bind-Value="payee.CollectName" />
                            <ValidationMessage For="@(() => payee.CollectName)" />
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="collectId" class="form-label required-field">身分證字號</label>
                            <InputText id="collectId" class="form-control" @bind-Value="payee.CollectId" />
                            <ValidationMessage For="@(() => payee.CollectId)" />
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="collectNo" class="form-label required-field">金融機構代號</label>
                            <InputText id="collectNo" class="form-control" @bind-Value="payee.CollectNo"
                                disabled="@isEditMode" />
                            <ValidationMessage For="@(() => payee.CollectNo)" />
                            @if (isEditMode)
                            {
                                <small class="form-text text-muted">編輯模式下無法修改金融機構代號</small>
                            }
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="collecAcc" class="form-label required-field">帳號</label>
                            <InputText id="collecAcc" class="form-control" @bind-Value="payee.CollecAcc"
                                disabled="@isEditMode" />
                            <ValidationMessage For="@(() => payee.CollecAcc)" />
                            @if (isEditMode)
                            {
                                <small class="form-text text-muted">編輯模式下無法修改帳號</small>
                            }
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="tel" class="form-label">電話</label>
                            <InputText id="tel" class="form-control" @bind-Value="payee.Tel" />
                            <ValidationMessage For="@(() => payee.Tel)" />
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="zip" class="form-label">郵遞區號</label>
                            <InputText id="zip" class="form-control" @bind-Value="payee.Zip" />
                            <ValidationMessage For="@(() => payee.Zip)" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="addr" class="form-label">地址</label>
                        <InputText id="addr" class="form-control" @bind-Value="payee.Addr" />
                        <ValidationMessage For="@(() => payee.Addr)" />
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="belongUnit" class="form-label">所屬單位</label>
                            <InputText id="belongUnit" class="form-control" @bind-Value="payee.BelongUnit" />
                            <ValidationMessage For="@(() => payee.BelongUnit)" />
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="belongAcc" class="form-label">所屬帳號</label>
                            <InputText id="belongAcc" class="form-control" @bind-Value="payee.BelongAcc" />
                            <ValidationMessage For="@(() => payee.BelongAcc)" />
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">資料是否提供單位共用</label>
                        <div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="shared" id="sharedYes" value="是"
                                    @onchange="@((ChangeEventArgs e) => payee.Shared = e.Value?.ToString())"
                                    checked="@(payee.Shared == "是")" />
                                <label class="form-check-label" for="sharedYes">是</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input" type="radio" name="shared" id="sharedNo" value="否"
                                    @onchange="@((ChangeEventArgs e) => payee.Shared = e.Value?.ToString())"
                                    checked="@(payee.Shared == "否")" />
                                <label class="form-check-label" for="sharedNo">否</label>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-secondary" @onclick="NavigateToList">
                            <i class="fas fa-arrow-left"></i> 返回清單
                        </button>

                        <div>
                            @if (isEditMode)
                            {
                                <button type="button" class="btn btn-warning me-2" @onclick="ResetForm">
                                    <i class="fas fa-undo"></i> 重置
                                </button>
                            }
                            <button type="submit" class="btn btn-primary" disabled="@isSaving">
                                @if (isSaving)
                                {
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                }
                                <i class="fas fa-save"></i> @(isEditMode ? "更新" : "新增")
                            </button>
                        </div>
                    </div>
                </EditForm>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public string? EncodedParams { get; set; }

    private Payee payee = new Payee();
    private bool isEditMode = false;
    private bool isLoading = false;
    private bool isSaving = false;
    private bool shouldNavigateToList = false;
    private string errorMessage = "";
    private string successMessage = "";
    private Payee? originalPayee = null;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (!string.IsNullOrEmpty(EncodedParams))
        {
            await LoadPayeeForEdit();
        }
        else
        {
            // 新增模式，初始化預設值
            payee = new Payee
            {
                Shared = "否",
                BelongUnit = _userState.UnitName,
                BelongAcc = _userState.Account
            };
        }
    }

    private async Task LoadPayeeForEdit()
    {
        try
        {
            isLoading = true;
            isEditMode = true;
            StateHasChanged();

            // 解碼參數
            var decodedParams = HttpUtility.UrlDecode(EncodedParams);
            var parts = decodedParams?.Split('|');

            if (parts?.Length != 2)
            {
                errorMessage = "參數格式錯誤";
                return;
            }

            var collectNo = parts[0];
            var collecAcc = parts[1];

            // 載入收款人資料
            var existingPayee = await PayeeService.GetPayeeByKeyAsync(collectNo, collecAcc);

            if (existingPayee == null)
            {
                errorMessage = "找不到指定的收款人資料";
                return;
            }

            payee = existingPayee;
            originalPayee = new Payee
            {
                CollectNo = existingPayee.CollectNo,
                CollecAcc = existingPayee.CollecAcc,
                CollectName = existingPayee.CollectName,
                CollectId = existingPayee.CollectId,
                Tel = existingPayee.Tel,
                Zip = existingPayee.Zip,
                Addr = existingPayee.Addr,
                BelongUnit = existingPayee.BelongUnit,
                BelongAcc = existingPayee.BelongAcc,
                Shared = existingPayee.Shared,
                Createdate = existingPayee.Createdate
            };
        }
        catch (Exception ex)
        {
            errorMessage = $"載入資料時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "載入收款人資料時發生錯誤");
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task HandleValidSubmit()
    {
        try
        {
            isSaving = true;
            errorMessage = "";
            successMessage = "";
            StateHasChanged();

            // 基本驗證
            if (string.IsNullOrWhiteSpace(payee.CollectName))
            {
                errorMessage = "收款人姓名為必填欄位";
                return;
            }

            if (string.IsNullOrWhiteSpace(payee.CollectId))
            {
                errorMessage = "身分證字號為必填欄位";
                return;
            }

            if (string.IsNullOrWhiteSpace(payee.CollectNo))
            {
                errorMessage = "金融機構代號為必填欄位";
                return;
            }

            if (string.IsNullOrWhiteSpace(payee.CollecAcc))
            {
                errorMessage = "帳號為必填欄位";
                return;
            }

            bool success = false;

            if (isEditMode)
            {
                // 更新現有資料
                success = await PayeeService.UpdatePayeeAsync(payee);
                if (success)
                {
                    successMessage = "更新成功！";
                    shouldNavigateToList = true;
                    Logger.LogInformation($"收款人資料更新成功：{payee.CollectName} ({payee.CollectNo}-{payee.CollecAcc})");
                }
                else
                {
                    errorMessage = "更新失敗，請稍後再試";
                }
            }
            else
            {
                // 新增資料 - 檢查帳號是否已存在
                if (!string.IsNullOrEmpty(payee.CollectNo) && !string.IsNullOrEmpty(payee.CollecAcc))
                {
                    var accountExists = await PayeeService.IsAccountExistsAsync(payee.CollectNo, payee.CollecAcc);
                    if (accountExists)
                    {
                        errorMessage = "此金融機構代號和帳號組合已存在！";
                        return;
                    }
                }

                // 設定建立日期為當前時間
                payee.Createdate = DateTime.Now;

                success = await PayeeService.AddPayeeAsync(payee);
                if (success)
                {
                    successMessage = "新增成功！";
                    shouldNavigateToList = true;
                    Logger.LogInformation($"收款人資料新增成功：{payee.CollectName} ({payee.CollectNo}-{payee.CollecAcc})");
                }
                else
                {
                    errorMessage = "新增失敗，請稍後再試";
                }
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"儲存時發生錯誤：{ex.Message}";
            Logger.LogError(ex, "儲存收款人資料時發生錯誤");
        }
        finally
        {
            isSaving = false;
            StateHasChanged();
        }
    }

    private void ResetForm()
    {
        if (originalPayee != null)
        {
            payee = new Payee
            {
                CollectNo = originalPayee.CollectNo,
                CollecAcc = originalPayee.CollecAcc,
                CollectName = originalPayee.CollectName,
                CollectId = originalPayee.CollectId,
                Tel = originalPayee.Tel,
                Zip = originalPayee.Zip,
                Addr = originalPayee.Addr,
                BelongUnit = originalPayee.BelongUnit,
                BelongAcc = originalPayee.BelongAcc,
                Shared = originalPayee.Shared,
                Createdate = originalPayee.Createdate
            };
        }

        errorMessage = "";
        successMessage = "";
        StateHasChanged();
    }

    private void NavigateToList()
    {
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ComRemit/PayeeList");
    }
}
