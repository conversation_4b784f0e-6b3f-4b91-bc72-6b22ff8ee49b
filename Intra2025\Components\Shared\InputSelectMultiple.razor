@using System.Linq.Expressions

<select multiple class="@AdditionalAttributes?["class"]" @onchange="OnChange" style="@AdditionalAttributes?["style"]">
    <option value="__all__" selected="@(IsAllSelected ? "selected" : null)">【全選】</option>
    @if (ChildContent != null)
    {
        @ChildContent
    }
</select>

@code {
    [Parameter] public List<string> Value { get; set; } = new();
    [Parameter] public EventCallback<List<string>> ValueChanged { get; set; }
    [Parameter] public Expression<Func<List<string>>> ValueExpression { get; set; } = null!;
    [Parameter(CaptureUnmatchedValues = true)] public Dictionary<string, object> AdditionalAttributes { get; set; } = new();
    [Parameter] public RenderFragment ChildContent { get; set; } = null!;

    [Parameter] public List<string> AllValues { get; set; } = new();

    private bool IsAllSelected => Value != null && AllValues != null && Value.Count == AllValues.Count &&
    !AllValues.Except(Value).Any();

    private async Task OnChange(ChangeEventArgs e)
    {
        var selected = new List<string>();
        if (e.Value is IEnumerable<object> values)
        {
            selected = values.Select(v => v?.ToString() ?? "").Where(s => !string.IsNullOrEmpty(s)).ToList();
        }
        else if (e.Value is string singleValue)
        {
            selected.Add(singleValue);
        }
        // 處理全選邏輯
        if (selected.Contains("__all__"))
        {
            // 若原本已全選，則取消全選
            if (IsAllSelected)
                selected.Clear();
            else
                selected = AllValues.ToList();
        }
        else if (IsAllSelected && !selected.Contains("__all__"))
        {
            // 若原本全選但取消了全選，則清空
            selected.Clear();
        }
        Value = selected;
        await ValueChanged.InvokeAsync(Value);
    }
}