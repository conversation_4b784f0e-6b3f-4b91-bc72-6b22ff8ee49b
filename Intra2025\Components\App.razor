﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="@(Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/') + "/")" />

    <link href="_content/core/jquery-ui.css" rel="stylesheet">

    <link rel="stylesheet" href="app.css" />
    <link rel="stylesheet" href="Intra2025.styles.css" />
    <link href="_content/themes/bootstrap5.css" rel="stylesheet" />
    <link href="bootstrap/bootstrap.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="sites.css" />
    <HeadOutlet @rendermode="InteractiveServer" />
</head>

<body>
    <Routes @rendermode="InteractiveServer" />
    <script src="_framework/blazor.web.js"></script>
</body>

</html>


<!-- jQuery (necessary for Bootstrap's JavaScript plugins) -->
<script src="_content/core/jquery-1.11.2.min.js"></script>
<script src="_content/core/jquery-ui.min.js"></script>
<!-- zh format package-->
<script src="_content/js/jquery-ui-datepicker-zh.js"></script>
<!-- ComRemit JavaScript functions -->
<script src="js/comremit.js"></script>

<script>
    function initializeDatepicker() {
        const currentYear = new Date().getFullYear() - 1911; // 取得民國年
        const yearRange = `${currentYear - 20}:${currentYear}`; // 往前20年範圍
        $(".datepicker").datepickerTW({
            changeMonth: true,
            changeYear: true,
            dateFormat: 'yy-mm-dd',
            yearRange: yearRange // 設定動態年份範圍
        });
    }

    function getDatepickerValue() {  //此func作用是取得UI上「日曆」的input值，回傳給blazor進行後端的資料處理
        return $(`.datepicker`).val() || "";
    }
</script>