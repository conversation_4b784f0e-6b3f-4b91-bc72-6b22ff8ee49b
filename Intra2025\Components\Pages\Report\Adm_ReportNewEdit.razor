﻿@page "/Adm_ReportNewEdit"
@page "/Adm_ReportNewEdit/{Id:int?}"
@using Intra2025.Components.Base
@using Intra2025.Models.Reports.Report
@using Intra2025.Servervices
@using Intra2025.Models.Report
@using Intra2025.Data
@using Microsoft.AspNetCore.Http
@using Microsoft.EntityFrameworkCore
@using System.Globalization
@using static Intra2025.Servervices.SsoService

@inject HttpClient Http
@inject ReportDbContext DbContext
@inherits BasePageComponent

<title>規劃報告書資料庫資料維護</title>
<h3>規劃報告書資料庫資料維護</h3>

<EditForm Model="report" OnValidSubmit="SaveReport" OnInvalidSubmit="InvalidSubmit">
    <DataAnnotationsValidator />
    <table class="form-table">
        <tr>
            <td>系統編號</td>
            <td>
                @if (report?.Sno != 0)
                {
                    <span>@report?.Sno</span>
                }
            </td>
        </tr>
        <tr>
            <td width="180px">
                <span id="requied">*</span>年度<br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【範例：填寫結案年度，如114年結案則選114年】
                </span>
            </td>
            <td>
                <select class="form-select" @bind="report.PlanYear" style="width:160px">
                    <option value="">請選擇年度</option>
                    @for (int year = DateTime.Now.Year - 1911; year >= 2008 - 1911; year--)
                    {
                        <option value="@year">@year</option>
                    }
                    <option value="1996">96年以前</option>
                </select>
                <ValidationMessage For="@(() => report.PlanYear)" />
            </td>
        </tr>
        <tr>
            <td><span id="requied">*</span>權責單位</td>
            <td>
                <select class="form-select" @bind="report.PowerDepart" style="width:160px">
                    <option value="">請選擇權責單位</option>
                    @if (powerDepartOptions != null && powerDepartOptions.Any())
                    {
                        @foreach (var option in powerDepartOptions)
                        {
                            <option value="@option">@option</option>
                        }
                    }
                </select>
                <ValidationMessage For="@(() => report.PowerDepart)" />
            </td>
        </tr>
        <tr>
            <td><span id="requied">*</span>計畫名稱</td>
            <td>
                <input type="text" class="form-control" @bind="report.Topic" />
                <ValidationMessage For="@(() => report.Topic)"></ValidationMessage>
            </td>
        </tr>
        <tr>
            <td>規劃開始日期 </td>
            <td>
                @* <input type="date" class="form-control" @bind="report.Begindate" style="width:160px" /> *@
                <div>
                    <select @bind="SelectedYearS" id="Year">
                        <option value="">選擇年份</option>
                        @foreach (var year in AvailableYears)
                        {
                            <option value="@year">@year</option>
                        }
                    </select>年
                    &nbsp;&nbsp;
                    <select @bind="SelectedMonthS" id="Month">
                        <option value="">選擇月份</option>
                        @foreach (var month in AvailableMonths)
                        {
                            <option value="@month">@month</option>
                        }
                    </select>月
                </div>
            </td>
        </tr>
        <tr>
            <td>規劃完成日期</td>
            <td>
                <div>
                    <select @bind="SelectedYearE" id="Year">
                        <option value="">選擇年份</option>
                        @foreach (var year in AvailableYears)
                        {
                            <option value="@year">@year</option>
                        }
                    </select>年
                    &nbsp;&nbsp;
                    <select @bind="SelectedMonthE" id="Month">
                        <option value="">選擇月份</option>
                        @foreach (var month in AvailableMonths)
                        {
                            <option value="@month">@month</option>
                        }
                    </select>月
                </div>
            </td>
        </tr>
        <tr>
            <td>聯絡科室</td>
            <td>
                <input type="text" class="form-control" @bind="report.ContactDepart" style="width:160px" />
            </td>
        </tr>
        <tr>
            <td>聯絡電話<br />
                <span style="font-size:xx-small;color:deepskyblue">【範例:03-9251000#3399】 </span>
            </td>
            <td>
                <input type="text" class="form-control" @bind="report.Tel" style="width:160px" />
            </td>
        </tr>
        <tr>
            <td>
                經費 (單位:新臺幣(元))<br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【範例:2,800,000】
                </span>
            </td>
            <td>
                <input type="text" class="form-control" @bind="displayFund" @oninput="OnInput" style="width:160px" />
            </td>
        </tr>
        <tr>
            <td>
                規劃單位(委托單位)<br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【ex:○○顧問公司、國立○○大學】
                </span>

            </td>
            <td>
                <input type="text" class="form-control" @bind="report.Consign" style="width:350px" />
            </td>
        </tr>
        <tr>
            <td>紙本位置</td>
            <td>
                <input type="text" class="form-control" @bind="report.PaperLocation" placeholder="預設為檔案室，如需修改位置請自行填入"
                    style="width:350px;" />
            </td>
        </tr>
        <tr>
            <td>內容摘要</td>
            <td>
                <textarea class="form-control" style="height: 260px" @bind="report.Content"></textarea>
            </td>
        </tr>
        <tr>
            <td>
                規劃報告書上傳檔<br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【支援的檔案格式為:
                    @foreach (var item in permittedExtensions)
                    {
                        @item.ToString()
                    }
                    】
                </span>
            </td>
            <td>
                <!--讀取原始上傳檔案-->
                @if (reportFiles != null && Id != null && reportFiles.TryGetValue(Id.ToString(), out var files) &&
                                files?.Any() == true)
                {
                    string fileName = "";
                    int i = 1;
                    @foreach (var file in files)
                    {
                        if (@file.Filedesc.Equals(""))
                            fileName = file.Filename;
                        else
                            fileName = file.Filedesc;
                        @(i.ToString() + ".")

                        <a href="@file.Filepath" style=" display: inline-block;padding-bottom: 10px;">@fileName</a>
                        <span class="btn btn-danger" style="color:lightseagreen;margin-left:3px;font-size:small"
                            @onclick="() => ConfirmDelete(file.Sno)">
                            移除
                        </span>
                        i++;
                        <br />
                    }
                }
                <!--上傳新檔案-->
                <InputFile OnChange="HandleFilesSelected" multiple />
                @if (selectedFiles != null)
                {
                    <div>
                        <h4>選擇檔案:</h4>
                        <ul>
                            @foreach (var file in selectedFiles)
                            {
                                <li>@file.Name (@file.Size KB)</li>
                            }
                        </ul>
                    </div>
                    @* <button @onclick="UploadFiles" class="btn btn-primary" type="button">上傳檔案</button>
                    <button @onclick="refreshFiles" class="btn btn-primary" type="button">重新整理</button> *@

                    @if (!string.IsNullOrEmpty(uploadMessage))
                    {
                        <div class="alert alert-success" role="alert">
                            <span style="background-color:aqua"> @uploadMessage</span>
                        </div>
                    }
                }

            </td>
        </tr>
        <tr>
            <td colspan="2">
                <button type="submit" class="btn btn-warning btn-lg">@(_isEditMode ? "案件更新" : "新增案件")</button>
                <button type="button" class="btn btn-info btn-lg" @onclick="BackToList">回通報資料清單</button>
            </td>
        </tr>
    </table>
</EditForm>

@code {
    [Parameter]
    public int? Id { get; set; }
    public bool DeleteShow { get; set; } = false;
    private int deleteId;
    private bool _isEditMode = false; //使用「新增」或「編輯」模式

    private int? SelectedYearS { get; set; }
    private int? SelectedMonthS { get; set; }
    private int? SelectedYearE { get; set; }
    private int? SelectedMonthE { get; set; }
    private List<int> AvailableYears = Enumerable.Range(105, (DateTime.Now.Year - (1911 + 105) + 1)) // 假設從民國105年開始
    .Reverse() // 反轉順序
    .ToList();
    private List<int> AvailableMonths = Enumerable.Range(1, 12).ToList(); // 1 月到 12 月
                                                                          // [Parameter]
                                                                          // public int? Sno { get; set; }

    private ELS_REPORTC? report = new();
    private List<string>? powerDepartOptions;

    private Dictionary<string, List<ELS_REPORT_FILEC>> reportFiles = new Dictionary<string, List<ELS_REPORT_FILEC>>();

    private string uploadMessage = string.Empty;
    private readonly long maxFileSize = 20 * 10 * 1024 * 1024;
    private readonly string[] permittedExtensions = { ".pdf", ".odt", ".ods", ".odp", ".zip", ".7z" };

    private string displayFund //經費
    {
        get => !string.IsNullOrEmpty(report.Fund) ? $"{decimal.Parse(report.Fund):N0}" : string.Empty;
        set
        {
            if (decimal.TryParse(value.Replace(",", ""), out var parsedValue))
            {
                report.Fund = parsedValue.ToString();
            }
            else
            {
                report.Fund = null;
            }
        }
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // 1. 判斷是否為編輯模式
            _isEditMode = Id != null && !string.IsNullOrEmpty(Id.ToString());

            if (_isEditMode)
            {
                // 編輯模式：載入指定的報告
                report = await DbContext.ELS_REPORT
                .SingleOrDefaultAsync(r => r.Sno == Id) ?? new ELS_REPORTC();

                // 判斷權限
                if (report.Postuserid == _userState.Account || _userState.DepCode == "113005" || _userState.Account == "stan1217")
                { }
                else
                {
                    // 如果是一般使用者且記錄不屬於該使用者的單位，顯示錯誤訊息
                    await JS.InvokeAsync<object>("alert", "您無權存取該記錄");
                    BackToList();
                    return;
                }

                // 初始化年份和月份選擇
                if (report.Begindate.HasValue)
                {
                    SelectedYearS = report.Begindate.Value.Year - 1911;
                    SelectedMonthS = report.Begindate.Value.Month;
                } // 初始化年份和月份選擇
                if (report.Enddate.HasValue)
                {
                    SelectedYearE = report.Enddate.Value.Year - 1911;
                    SelectedMonthE = report.Enddate.Value.Month;
                }
            }
            else
            {
                // 新增模式：初始化空白表單
                report = new ELS_REPORTC();
            }

            // 2. 載入權限部門選項
            powerDepartOptions = await DbContext.ELS_REPORT
            .Where(r => !string.IsNullOrEmpty(r.PowerDepart))
            .Select(r => r.PowerDepart)
            .Distinct()
            .ToListAsync();

            // 3. 抓取報告上傳檔案
            var files = await DbContext.ELS_REPORT_FILES.ToListAsync();
            foreach (var file in files)
            {
                if (!reportFiles.ContainsKey(file.Bbssno))
                {
                    reportFiles[file.Bbssno] = new List<ELS_REPORT_FILEC>();
                }
                reportFiles[file.Bbssno].Add(file);
            }

            // 強制更新 UI
            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine(ex);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        @if (report == null)
        {
            // await JSRuntime.InvokeVoidAsync("alert", "無此資料或無此資料之編輯權限...");
            // Navigation.NavigateTo("/Adm_ReportList"); //無SSO時要導至登入頁面
        }
    }

    private void OnInput(ChangeEventArgs e)
    {
        // 直接更新綁定值，保持即時格式化
        displayFund = e.Value?.ToString() ?? string.Empty;
    }

    private async Task InvalidSubmit()
    {
        await Task.CompletedTask;
    }

    private async Task CloseModal()
    {
        //await OnClose.InvokeAsync(false);
    }

    private void BackToList() //回「資料清單」
    {
        NavigationManager.NavigateTo("/Adm_ReportList", true);
        //return;
    }

    private async Task HandleOverlayClick(MouseEventArgs e)
    {
        this.DeleteShow = true;
    }

    private async Task onDelete(int id)
    {
        deleteId = id;
        this.DeleteShow = true;
    }

    private void ConfirmRemoveFile(int sno)
    {
        var report = DbContext.ELS_REPORT_FILES
        .Where(r => r.Sno == sno)
        .ExecuteDeleteAsync();

        // 確認刪除後更新頁面
        //LoadReports("");
        NavigationManager.NavigateTo($"/Adm_ReportNewEdit/{Id}", true);
    }

    private async Task SaveReport() // 資料儲存
    {
        if (report == null)
        {
            await JS.InvokeAsync<object>("alert", "無效的報告資料，無法儲存。");
            return;
        }

        bool isSuccess = false;

        if (report.Sno == 0) // 判斷是否為新增模式
        {
            isSuccess = await AddReportAsync(); // 呼叫新增方法，並確保其執行完成
        }
        else
        {
            isSuccess = await UpdateReportAsync(); // 呼叫編輯方法，並確保其執行完成
        }

        if (isSuccess)
        {
            // 確保成功後再跳轉
            NavigationManager.NavigateTo("/Adm_ReportList");
        }
        else
        {
            await JS.InvokeAsync<object>("alert", "儲存失敗，請稍後再試。");
        }
    }

    private async Task<bool> AddReportAsync() // 新增規劃報告書
    {
        // 驗證資料
        if (!await ValidateRecordAsync(report))
        {
            return false;
        }

        // 獲取當前最大 Sno，生成新 Sno
        int maxSno = await DbContext.ELS_REPORT_FILES
        .Where(record => record.Bbssno != null && record.Bbssno != "")
        .Select(record => EF.Functions.Like(record.Bbssno, "[0-9]%") ? (int?)Convert.ToInt32(record.Bbssno) : null)
        .MaxAsync() ?? 0; // 如果資料表內無有效數值，則預設為 0
                          //report.Sno = maxSno + 1;

        // 設置新增模式的預設值
        report.Typesno = 1;
        report.State = 1; // 1: 新增
        report.Enabled = true; // 啟用
        report.Createuserid = _userState.Account;
        report.Createdate = DateTime.Now;
        report.Postdate = DateTime.Now;
        report.Postuserid = _userState.Account;
        report.Postdepartid = _userState.DepCode.Substring(0, 3);
        report.Ownuserid = _userState.Account;
        report.Owndepartid = _userState.DepCode;

        // 設置開始和結束日期
        report.Begindate = new DateTime(SelectedYearS.Value + 1911, SelectedMonthS.Value, 1);
        report.Enddate = new DateTime(SelectedYearE.Value + 1911, SelectedMonthE.Value, 1);

        // 新增記錄
        await DbContext.ELS_REPORT.AddAsync(report);
        await DbContext.SaveChangesAsync(); // 確保報告儲存成功，避免後續檔案關聯失敗

        if (selectedFiles != null && selectedFiles.Any())// 只有當 selectedFiles 有檔案時才執行
        {
            // 處理檔案
            var uploadDirectory = Path.Combine(Environment.CurrentDirectory, "wwwroot/uploads/Reports");
            if (!Directory.Exists(uploadDirectory))
            {
                Directory.CreateDirectory(uploadDirectory);
            }

            foreach (var file in selectedFiles)
            {
                var uniqueFileName = $"{Guid.NewGuid()}_{file.Name}";
                var filePath = Path.Combine(uploadDirectory, uniqueFileName);

                using (var fileStream = new FileStream(filePath, FileMode.Create))
                {
                    await file.OpenReadStream(maxFileSize).CopyToAsync(fileStream);
                }

                // 儲存檔案資訊到資料表
                var uploadedFile = new ELS_REPORT_FILEC
                {
                    Bbssno = report.Sno.ToString(), // 假設這是正確的外鍵關聯值
                    Filename = Path.GetFileName(file.Name),
                    Filepath = $"/uploads/Reports/{uniqueFileName}", // 使用相對路徑儲存檔案路徑
                    Filedesc = string.Empty, // 預設描述為空，可根據需要填寫
                    Postdepartid = _userState.DepCode.Substring(0, 3), // 使用當前使用者的部門 ID
                    Createuserid = _userState.Account, // 使用當前使用者帳號
                    Createdate = DateTime.Now // 設定創建日期
                };

                DbContext.ELS_REPORT_FILES.Add(uploadedFile);
            }

            await DbContext.SaveChangesAsync();
        }

        // 清空已選檔案
        selectedFiles = null;

        // 成功提示
        await JS.InvokeAsync<object>("alert", $"案件 {report.Sno} 新增成功");

        // 導向案件列表
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList");

        return true; // 新增成功
    }


    private async Task<bool> UpdateReportAsync()
    {
        // 查找現有記錄
        var existingReport = await DbContext.ELS_REPORT.FindAsync(report.Sno);
        if (existingReport != null)
        {
            // 更新欄位
            existingReport.PlanYear = report.PlanYear;
            existingReport.PowerDepart = report.PowerDepart;
            existingReport.Topic = report.Topic;
            existingReport.Tel = report.Tel;
            existingReport.Content = report.Content;
            existingReport.ContactDepart = report.ContactDepart;
            existingReport.Fund = report.Fund;
            existingReport.PaperLocation = report.PaperLocation;

            // 更新開始和結束日期
            existingReport.Begindate = new DateTime(SelectedYearS.Value + 1911, SelectedMonthS.Value, 1);
            existingReport.Enddate = new DateTime(SelectedYearE.Value + 1911, SelectedMonthE.Value, 1);

            // 保存變更
            await DbContext.SaveChangesAsync();

            // 確保 selectedFiles 不為 null 或空集合
            if (selectedFiles != null && selectedFiles.Any())
            {
                // 處理檔案
                var uploadDirectory = Path.Combine(Environment.CurrentDirectory, "wwwroot/uploads/Reports");
                if (!Directory.Exists(uploadDirectory))
                {
                    Directory.CreateDirectory(uploadDirectory);
                }

                foreach (var file in selectedFiles)
                {
                    var uniqueFileName = $"{Guid.NewGuid()}_{file.Name}";
                    var filePath = Path.Combine(uploadDirectory, uniqueFileName);

                    using (var fileStream = new FileStream(filePath, FileMode.Create))
                    {
                        await file.OpenReadStream(maxFileSize).CopyToAsync(fileStream);
                    }

                    // 儲存檔案資訊到資料表
                    var uploadedFile = new ELS_REPORT_FILEC
                    {
                        Bbssno = report.Sno.ToString(), // 假設這是正確的外鍵關聯值
                        Filename = Path.GetFileName(file.Name),
                        Filepath = $"/uploads/Reports/{uniqueFileName}", // 使用相對路徑儲存檔案路徑
                        Filedesc = string.Empty, // 預設描述為空，可根據需要填寫
                        Postdepartid = _userState.DepCode.Substring(0, 3), // 使用當前使用者的部門 ID
                        Createuserid = _userState.Account, // 使用當前使用者帳號
                        Createdate = DateTime.Now // 設定創建日期
                    };

                    DbContext.ELS_REPORT_FILES.Add(uploadedFile);
                }

                await DbContext.SaveChangesAsync();
            }

            // 清空已選檔案
            selectedFiles = null;

            // 成功提示
            await JS.InvokeAsync<object>("alert", $"案件 {report.Sno} 更新成功");
            return true; // 更新成功

            // 導向案件列表
            NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/CareCaseList");
        }
        else
        {
            // 替換 Console.Error.WriteLine 為 alert 提示
            await JS.InvokeAsync<object>("alert", $"無法找到編號為 {report.Sno} 的報告，請確認後再試。");
            return false; // 更新成功
        }
    }




    private void Cancel()
    {
        NavigationManager.NavigateTo("/Adm_ReportList", forceLoad: true);
    }

    // private void DeleteFile(int id)
    // {
    // var report = DbContext.ELS_REPORT_FILES
    // .Where(r => r.Sno == id)
    // .ExecuteDeleteAsync();

    // NavigationManager.NavigateTo("/Adm_ReportList", true);
    // }


    // 刪除檔案前的確認對話框
    private async Task ConfirmDelete(int sno)
    {
        // 判斷權限
        // 確保只有Postuserid為當前登入使用者，且DepCode為113005才能刪除 ，113005:綜規科
        if (report.Postuserid == _userState.Account || _userState.DepCode == "113005" || _userState.Account == "stan1217")
        { }
        else
        {
            // 如果是一般使用者且記錄不屬於該使用者的單位，顯示錯誤訊息
            await JS.InvokeAsync<object>("alert", "您無權存取該記錄");
            BackToList();
            return;
        }

        var confirmed = await JS.InvokeAsync<bool>("confirm", "您確定要刪除此檔案嗎？");
        if (confirmed)
        {
            await DeleteFile(sno); // 若確認，則刪除檔案
        }
    }

    // 刪除檔案
    private async Task DeleteFile(int sno)
    {
        var file = await DbContext.ELS_REPORT_FILES.FindAsync(sno);
        DbContext.ELS_REPORT_FILES.Remove(file);

        await DbContext.SaveChangesAsync();
        NavigationManager.NavigateTo($"/Adm_ReportNewEdit/{Id}", true);
    }

    private IBrowserFile[]? selectedFiles;

    private void HandleFilesSelected(InputFileChangeEventArgs e)
    {
        selectedFiles = e.GetMultipleFiles(5).ToArray();// 限制最多上傳 5 個檔案
    }

    private async Task refreshFiles()
    {
        NavigationManager.NavigateTo($"/Adm_ReportNewEdit/{Id}", true);
    }

    private async Task UploadFiles()
    {
        foreach (var file in selectedFiles)
        {
            var extension = Path.GetExtension(file.Name).ToLowerInvariant();

            if (file.Size > maxFileSize)
            {
                uploadMessage = $"File '{file.Name}' 超過最大檔案上限: {maxFileSize / 1024 / 1024} MB.";
                return;
            }

            if (!permittedExtensions.Contains(extension))
            {
                uploadMessage = $"File '{file.Name}' 是非支援的檔案格式.";
                return;
            }

            var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss", CultureInfo.InvariantCulture);
            var fileName = $"{timestamp}_{file.Name}";
            var filePath = Path.Combine(env.WebRootPath, "uploads/Reports", fileName);

            // 確保 uploads 資料夾存在
            //Directory.CreateDirectory(Path.GetDirectoryName(filePath));

            await using var fileStream = new FileStream(filePath, FileMode.Create);
            await file.OpenReadStream(maxFileSize).CopyToAsync(fileStream);


            var newReportFile = new ELS_REPORT_FILEC
            {
                Bbssno = Id.ToString(),
                Filedesc = file.Name,
                Filename = file.Name,
                Filepath = filePath,
                Createdate = DateTime.Now
            };

            await DbContext.ELS_REPORT_FILES.AddAsync(newReportFile);
            await DbContext.SaveChangesAsync();
        }

        uploadMessage = "檔案已上傳成功.";
    }

    private async Task<bool> ValidateRecordAsync(ELS_REPORTC report)
    {
        // 檢查年度
        if (!report.PlanYear.HasValue)
        {
            await JS.InvokeAsync<object>("alert", "【年度】為必填欄位");
            await Task.Delay(100);
            return false;
        }

        // 檢查權責單位
        if (string.IsNullOrWhiteSpace(report.PowerDepart))
        {
            await JS.InvokeAsync<object>("alert", "【權責單位】為必填欄位");
            await Task.Delay(100);
            return false;
        }

        // 檢查計畫名稱
        if (string.IsNullOrWhiteSpace(report.Topic))
        {
            await JS.InvokeAsync<object>("alert", "【計畫名稱】為必填欄位");
            await Task.Delay(100);
            return false;
        }

        // 檢查規劃開始日期
        if (!SelectedYearS.HasValue || !SelectedMonthS.HasValue)
        {
            await JS.InvokeAsync<object>("alert", "【規劃開始日期】的年份和月份需完整選擇");
            await Task.Delay(100);
            return false;
        }
        else
        {
            report.Begindate = new DateTime(SelectedYearS.Value, SelectedMonthS.Value, 1);
        }

        // 檢查規劃完成日期
        if (!SelectedYearE.HasValue || !SelectedMonthE.HasValue)
        {
            await JS.InvokeAsync<object>("alert", "【規劃完成日期】的年份和月份需完整選擇");
            await Task.Delay(100);
            return false;
        }
        else
        {
            report.Enddate = new DateTime(SelectedYearE.Value, SelectedMonthE.Value, 1);
        }

        return true; // 所有欄位都通過檢查
    }



}
