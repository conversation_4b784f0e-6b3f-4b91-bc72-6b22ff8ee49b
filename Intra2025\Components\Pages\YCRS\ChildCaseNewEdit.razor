@page "/ChildCaseNewEdit"
@page "/ChildCaseNewEdit/{ID?}"
@inject YCRSDbContext _context
@using Intra2025.Components.Base
@using Intra2025.Data
@using Intra2025.Models
@using Microsoft.AspNetCore.Components.Forms
@using System.Globalization
@using Microsoft.EntityFrameworkCore
@using System.Text.Json
@using static Intra2025.Servervices.SsoService
@inject EmailService EmailService
@inherits BasePageComponent
@using Microsoft.AspNetCore.WebUtilities
@using System.Web

<title>守護寶貝即時通(資料編輯)</title>
<h1>守護寶貝即時通</h1>
<h3 class="text-center mt-4">資料維護</h3>

<head>
    <link href="/modals.css" rel="stylesheet" />
</head>


<EditForm Model="ChildRecord" OnValidSubmit="HandleValidSubmit" class="form-container">
    <DataAnnotationsValidator />

    <table class="form-table">
        <tr>
            <td style="width:290px"><label for="ChildName">系統編號</label></td>
            <td>
                <span>@ChildRecord.Id</span>
            </td>
        </tr>
        <tr>
            <td width="275px"><label for="BelongDate">案件所屬日期(月份)</label> <span
                    style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <div>
                    <select @bind="SelectedYear" id="Year">
                        <option value="">選擇年份</option>
                        @foreach (var year in AvailableYears)
                        {
                            <option value="@year">@year</option>
                        }
                    </select>年
                    &nbsp;&nbsp;
                    <select @bind="SelectedMonth" id="Month">
                        <option value="">選擇月份</option>
                        @foreach (var month in AvailableMonths)
                        {
                            <option value="@month">@month</option>
                        }
                    </select>月
                </div>
            </td>
        </tr>
        <tr>
            <td><label for="ChildName">子女姓名</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <InputText id="ChildName" @bind-Value="ChildRecord.ChildName" class="form-control"
                    style="width:160px" />
                <ValidationMessage For="@(() => ChildRecord.ChildName)" class="validation-inline" />
            </td>
        </tr>
        <tr>
            <td><label for="ChildIdNumber">子女身分證字號</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span>
            </td>
            <td>
                <InputText id="ChildIdNumber" @bind-Value="ChildRecord.ChildIdNumber" maxlength="10"
                    class="form-control" style="width:160px" />
                <ValidationMessage For="@(() => ChildRecord.ChildIdNumber)" class="validation-inline" />
            </td>
        </tr>
        <tr>
            <td><label for="BirthDate">子女出生年月日</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span> </td>
            <td>
                <input type="text" class="form-control datepicker" style="width:160px" id="MGDate" name="BirthDate"
                    @bind="BirthDateString" />
            </td>
        </tr>
        <!-- 監護人 1 區塊 -->

        <tr class="highlight">
            <td><label for="Parent1Name">監護人(父親)姓名</label></td>
            <td style="display: flex; align-items: center; gap: 10px;">
                <InputText id="Parent1Name" @bind-Value="ChildRecord.Parent1Name" class="form-control"
                    style="width:160px" />
                <InputCheckbox @bind-Value="ChildRecord.Pt1Imprison" id="Visitcare" /> 入監人口

            </td>
        </tr>
        <tr class="highlight">
            <td><label for="Parent1Id">監護人(父親)身分證字號</label></td>
            <td>
                <InputText id="Parent1Id" @bind-Value="ChildRecord.Parent1Id" maxlength="10" class="form-control"
                    style="width:160px" />
            </td>
        </tr>

        <!-- 監護人 2 區塊 -->
        <tr class="highlight2">
            <td><label for="Parent2Name">監護人(母親)姓名</label></td>
            <td style="display: flex; align-items: center; gap: 10px;">
                <InputText id="Parent2Name" @bind-Value="ChildRecord.Parent2Name" class="form-control"
                    style="width:160px" />
                <InputCheckbox @bind-Value="ChildRecord.Pt2Imprison" id="Visitcare" /> 入監人口
            </td>
        </tr>
        <tr class="highlight2">
            <td><label for="Parent2Id">監護人(母親)身分證字號</label></td>
            <td>
                <InputText id="Parent2Id" @bind-Value="ChildRecord.Parent2Id" maxlength="10" class="form-control"
                    style="width:160px" />
            </td>
        </tr>

        <tr>
            <td>
                <label for="Contactor">聯絡人姓名/關係</label><br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【請註明為誰的聯絡方式 ex:王大美/母】
                </span>
            </td>
            <td style="width:160px">
                <InputText id="Contactor" @bind-Value="ChildRecord.Contactor" class="form-control" />
            </td>
        </tr>
        <tr>
            <td>
                <label for="ContactCommunication">聯絡電話</label><br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【ex: 0912-345678 / 03-9251000】
                </span>
            </td>
            <td style="width:160px">
                <InputText id="ContactCommunication" @bind-Value="ChildRecord.ContactCommunication"
                    class="form-control" />
            </td>
        </tr>

        <tr>
            <td>
                <label for="Attributes">個案類型</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span>
            </td>
            <td>
                <select @bind="ChildRecord.AttributesId" class="form-select">
                    <option value="" disabled selected>請選擇</option> <!-- 預設選項 -->
                    @if (categories != null && categories.Any())
                    {
                        @foreach (var category in categories)
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    }
                    else
                    {
                        <option disabled>Loading categories...</option>
                    }
                </select>
            </td>
        </tr>
        <tr>
            <td>
                <label for="Contactor">戶籍地址</label> <span style="font-size: 12px; color: red;">&nbsp;*必填</span>
            </td>
            <td>
                <InputText id="HouseholdAddress" style="width:400px" @bind-Value="ChildRecord.HouseholdAddress"
                    class="form-control" />
                <ValidationMessage For="@(() => ChildRecord.HouseholdAddress)" class="validation-inline" />
            </td>
        </tr>
        <tr>
            <td>
                <label for="CopyAddress">實際居住地址(聯絡地址)</label>
            </td>
            <td>
                <InputCheckbox @bind-Value="IsSameAddress" /><span
                    style="font-size:x-small;color:dodgerblue">同戶籍地址</span> <br />
                <InputText id="CurrentAddress" style="width:400px" @bind-Value="ChildRecord.CurrentAddress"
                    class="form-control" />
            </td>
        </tr>

        <tr>
            <td>
                <label for="Remarks">備註</label><br />
                <span style="font-size:xx-small;color:deepskyblue">
                    【ex: 父親入監，母親攜個案返回新北市娘家居住】 <br /> 200字為限
                </span>
            </td>
            <td>
                <textarea id="Remarks" class="form-control" style="width: 400px;height: 130px"
                    @bind="ChildRecord.Remarks" maxlength="200" rows="5"></textarea>
            </td>
        </tr>
        <tr>
            <td>
                <label for="Contactor">案件新增日期</label>
            </td>
            <td> <span>@ChildRecord.CreateDate </span> </td>
        </tr>
        <tr>
            <td>
                <label for="Contactor">案件所屬戶所</label>
            </td>
            <td> <span>@caseBelongName</span> &nbsp;<span> @(string.IsNullOrEmpty(ChildRecord.CreateName) ? "" :
                                        $"(承辦人:{ChildRecord.CreateName})") </span> </td>
        </tr>
    </table>

    <button type="submit" class="btn btn-warning btn-lg">@(_isEditMode ? "案件更新" : "新增案件")</button>
    <button type="button" class="btn btn-info btn-lg" @onclick="BackToList">回通報資料清單</button>
</EditForm>

<!-- 成功訊息 Modal -->
<div class="modal" tabindex="-1" role="dialog" style="display: @(UpdateSuccess ? "block" : "none")">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body">
                <p>您的資料已成功更新！</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" @onclick="CloseModal">確認</button>
            </div>
        </div>
    </div>
</div>

@if (SuccessMessage != null)
{
    <div class="alert alert-success mt-3 text-center">@SuccessMessage</div>
}
@code {
    [Parameter]
    public string? ID { get; set; } // 取得 URL 中的 id 參數
    private bool _isEditMode;

    private string? clientIp;
    private string? caseBelongName = "";



    private List<JsonDataLoader.Category>? categories = new();
    private List<JsonDataLoader.CaseBelong>? caseBelongs = new();
    private List<string>? recipients = new();

    private YCRS_ChildRecord ChildRecord = new YCRS_ChildRecord();
    private string? SuccessMessage;



    public bool DeleteShow { get; set; } = false;

    private int? SelectedYear { get; set; }
    private int? SelectedMonth { get; set; }

    private List<int> AvailableYears = Enumerable.Range(112, (DateTime.Now.Year - (1911 + 112) + 1))
    .Reverse() // 反轉順序
    .ToList();
    private List<int> AvailableMonths = Enumerable.Range(1, 12).ToList(); // 1 月到 12 月


    private bool _isSameAddress = false;
    private bool IsSameAddress
    {
        get => _isSameAddress;
        set
        {
            _isSameAddress = value;
            if (_isSameAddress)
            {
                ChildRecord.CurrentAddress = ChildRecord.HouseholdAddress;
            }
            else
            {
                ChildRecord.CurrentAddress = string.Empty;
            }
        }
    }

    private string BirthDateString
    {
        get
        {
            if (ChildRecord.BirthDate.HasValue)
            {
                var taiwanYear = ChildRecord.BirthDate.Value.Year - 1911; // 西元轉民國年
                return $"{taiwanYear:D3}-{ChildRecord.BirthDate.Value.Month:D2}-{ChildRecord.BirthDate.Value.Day:D2}";
            }
            return string.Empty;
        }
        set
        {
            if (!string.IsNullOrWhiteSpace(value))
            {
                var parts = value.Split('-');
                if (parts.Length == 3 && int.TryParse(parts[0], out var taiwanYear))
                {
                    ChildRecord.BirthDate = new DateTime(taiwanYear + 1911, int.Parse(parts[1]), int.Parse(parts[2]));
                }
            }
        }
    }

    private bool UpdateSuccess { get; set; } = false;

    private string? searchTerm;
    private string? dt1Str;
    private string? dt2Str;
    private int? currentPage;

    private void ShowSuccessModal()
    {
        UpdateSuccess = true;
        StateHasChanged(); // 強制 UI 重新渲染
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ChildCaseList");
    }

    private void CloseModal()
    {
        UpdateSuccess = false;
        StateHasChanged(); // 強制 UI 重新渲染
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // 解析 QueryString
            var uri = NavigationManager.ToAbsoluteUri(NavigationManager.Uri);
            var query = QueryHelpers.ParseQuery(uri.Query);
            if (query.TryGetValue("searchTerm", out var qSearchTerm))
                searchTerm = qSearchTerm;
            if (query.TryGetValue("dt1", out var qDt1))
                dt1Str = qDt1;
            if (query.TryGetValue("dt2", out var qDt2))
                dt2Str = qDt2;
            if (query.TryGetValue("currentPage", out var qPage) && int.TryParse(qPage, out var parsedPage))
                currentPage = parsedPage;

            // 判斷是否進入編輯模式
            _isEditMode = !string.IsNullOrEmpty(ID);

            await LoadCommonData(); // 加載通用數據

            if (_isEditMode)
            {
                await LoadExistingRecord(); // 加載現有記錄
            }
        }
        catch (Exception ex)
        {
            SuccessMessage = $"初始化失敗: {ex.Message}";
            Console.Error.WriteLine(ex);
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        // 保證 Datepicker 會在每次渲染後執行初始化
        await JS.InvokeVoidAsync("initializeDatepicker");
    }

    private async Task LoadExistingRecord()
    {
        // This method loads a record for editing based on the provided 'ID'.
        try
        {
            var record = await _context.YCRS_ChildRecord.FindAsync(ID);

            if (record == null)
            {
                // 如果記錄不存在，顯示錯誤訊息
                await JS.InvokeAsync<object>("alert", "無此案件資料");
                await BackToList();
                return;
            }

            // 根據權限過濾單一記錄
            if (!_userState.IsAdmin && record.CaseBelongId != _userState.UnitCode)
            {
                // 如果是一般使用者且記錄不屬於該使用者的單位，顯示錯誤訊息
                await JS.InvokeAsync<object>("alert", "您無權存取該記錄");
                await BackToList();
                return;
            }
            if (record != null)
            {
                if (record.BelongDate.HasValue)
                {
                    var gregorianDate = record.BelongDate.Value;
                    var taiwanYear = gregorianDate.Year - 1911; // 轉换為民國年
                    SelectedYear = taiwanYear;
                    SelectedMonth = gregorianDate.Month;
                }
                if (record.BirthDate.HasValue)
                {
                    var gregorianDate = record.BirthDate.Value;
                    var taiwanYear = gregorianDate.Year - 1911; // 轉換為民國年
                    BirthDateString = $"{taiwanYear:D3}-{gregorianDate.Month:D2}-{gregorianDate.Day:D2}";
                }
                else
                {
                    BirthDateString = string.Empty;
                }

                ChildRecord = record;
            }
            else
            {
                ChildRecord = new YCRS_ChildRecord();
                SuccessMessage = "找不到該筆資料";
            }
        }
        catch (Exception ex)
        {
            SuccessMessage = $"記錄加載失敗: {ex.Message}";
        }

        if (caseBelongs != null && caseBelongs.Any())
        {
            var caseBelong = caseBelongs.FirstOrDefault(c => c.Id == ChildRecord.CaseBelongId);
            if (caseBelong != null) caseBelongName = caseBelong.Name;
        }
    }


    private async Task LoadCommonData()
    {
        clientIp = ClientIpService.ClientIp;

        var jsonDataLoader = new JsonDataLoader(env);
        categories = await jsonDataLoader.LoadCategoriesFromJsonAsync("A"); //取得「案件所屬類別」的資料
        caseBelongs = await jsonDataLoader.LoadCaseBelongFromJsonAsync(); //取得「案件所屬單位」的資料
        recipients = await jsonDataLoader.LoadRecipientsFromJsonAsync(); //取得通知MAIL的名單
    }

    private async Task HandleValidSubmit()
    {
        if (!_isEditMode)
        {
            await CreateChildRecordAsync(ChildRecord);
        }
        else
        {
            await UpdateChildRecordAsync(ChildRecord);
        }

        if (!ChildRecord.IsMailed) //判斷是否已MAIL，若尚未MAIL則發送MAIL
        {
            await SendEmailNotification();

            // 更新資料庫
            ChildRecord.IsMailed = true;
            _context.YCRS_ChildRecord.Update(ChildRecord);
            await _context.SaveChangesAsync();
        }

    }

    private async Task CreateChildRecordAsync(YCRS_ChildRecord record)
    {
        if (!await ValidateRecordAsync(record)) //進行必填欄位進行檢查
        {
            return;
        }

        try
        {
            string currentYearMonth = DateTime.Now.ToString("yyMM");
            BirthDateString = await JS.InvokeAsync<string>("getDatepickerValue");


            // 獲取目前資料庫中最大序號的 Id
            string? nowMaxId = await _context.YCRS_ChildRecord
            .AsNoTracking()
            .Where(r => r.Id != null && r.Id.StartsWith($"A{currentYearMonth}"))
            .OrderByDescending(r => r.Id)
            .Select(r => r.Id)
            .FirstOrDefaultAsync();

            // 設置新的流水號
            record.Id = string.IsNullOrEmpty(nowMaxId) || nowMaxId.Length != 8
            ? $"A{currentYearMonth}001"
            : $"A{currentYearMonth}{(int.Parse(nowMaxId.Substring(5, 3)) + 1):D3}";

            // 預設欄位值
            record.CreateDate = DateTime.Now;
            record.IsChecked = false; //案件狀態，預設是「false(尚未處理)」
            record.IsMailed = false; //案件通知情形，預設是「false(尚未MAIL)」
            record.CaseBelongId = _userState.UnitCode; //案件所屬戶所

            // 設置創建日期
            record.CreateDate = DateTime.Now;
            // 設定所屬月份
            record.BelongDate = new DateTime((SelectedYear ?? 0) + 1911, (SelectedMonth ?? 1), 1);
            record.CreateName = _userState.UserName;

            await AccessLogHelper.SaveAccessLogAsync(_context, record.Id ?? "", "資料新增", _userState.Account, clientIp ?? ""); // 寫入新增
            Log
            await _context.YCRS_ChildRecord.AddAsync(record);
            await _context.SaveChangesAsync();

            await JS.InvokeAsync<object>("alert", $"案件 {record.Id} 新增成功");
            await BackToList();

        }
        catch (Exception ex)
        {
            SuccessMessage = $"資料新增失敗: {ex.Message}";
        }
    }

    private async Task<bool> ValidateRecordAsync(YCRS_ChildRecord record) //進行必填欄位進行檢查
    {
        if (string.IsNullOrWhiteSpace(record.AttributesId))
        {
            await JS.InvokeAsync<object>("alert", "個案類型為必選欄位");
            return false;
        }

        if (!SelectedYear.HasValue || !SelectedMonth.HasValue)
        {
            await JS.InvokeAsync<object>("alert", "案件所屬月份為必填欄位");
            return false;
        }

        if (string.IsNullOrWhiteSpace(record.ChildIdNumber))
        {
            await JS.InvokeAsync<object>("alert", "子女身分證字號為必填欄位");
            return false;
        }

        if (string.IsNullOrWhiteSpace(record.ChildName))
        {
            await JS.InvokeAsync<object>("alert", "子女姓名為必填欄位");
            return false;
        }

        if (BirthDateString == null)
        {
            await JS.InvokeAsync<object>("alert", "子女生日為必填欄位");
            return false;
        }

        if (string.IsNullOrWhiteSpace(record.HouseholdAddress))
        {
            await JS.InvokeAsync<object>("alert", "戶籍地址為必填欄位");
            return false;
        }

        return true;
    }


    private async Task UpdateChildRecordAsync(YCRS_ChildRecord record)
    {
        try
        {
            BirthDateString = await JS.InvokeAsync<string>("getDatepickerValue");

            // 驗證輸入資料
            if (record == null)
            {
                await JS.InvokeAsync<object>("alert", "更新的案件資料不能為空");
                return;
            }

            // 判斷是否允許更新
            if (record.IsChecked == true)
            {
                await JS.InvokeAsync<object>("alert", "此案件已由社會處【確認】(進行鎖定)，無法進行編輯");
                return;
            }

            if (!await ValidateRecordAsync(record)) //進行必填欄位進行檢查
            {
                return;
            }

            record.BelongDate = new DateTime((SelectedYear ?? 0) + 1911, (SelectedMonth ?? 1), 1);

            await AccessLogHelper.SaveAccessLogAsync(_context, record.Id ?? "", "資料編輯", _userState.Account, clientIp ?? "");

            _context.YCRS_ChildRecord.Update(record);
            await _context.SaveChangesAsync();


            await JS.InvokeAsync<object>("alert", $"案件 {record.Id} 更新成功");
            await BackToList();
        }
        catch (Exception ex)
        {
            SuccessMessage = $"資料更新失敗: {ex.Message}";
        }
    }

    private Task BackToList()
    {
        // 返回時帶回 QueryString
        var query = $"?searchTerm={HttpUtility.UrlEncode(searchTerm)}&dt1={dt1Str}&dt2={dt2Str}&currentPage={currentPage}";
        NavigationManager.NavigateTo($"{Environment.GetEnvironmentVariable("ASPNETCORE_APPL_PATH")?.TrimEnd('/')}/ChildCaseList{query}",
        true);
        return Task.CompletedTask;
    }

    private async Task SendEmailNotification()
    {
        string subject = $"守護寶貝即時通 ~ 【{ChildRecord.ChildName}案件】的資料登錄通知(案件編號:{ChildRecord.Id})";
        string body = $"<p>【{ChildRecord.ChildName}案件】的資料登錄通知(案件編號:{ChildRecord.Id})</p>";
        body += $"<p> 您所處理的案件，請至「員工入口網 / 業務系統(一) / 社福資訊專區 / 守護寶貝即時通」進行承辦。</p>";

        await EmailService.SendEmailAsync(recipients ?? new List<string>(), subject, body);
        Console.WriteLine("郵件發送成功");
    }
}
