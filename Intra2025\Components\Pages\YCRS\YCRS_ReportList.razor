﻿@page "/YCRS_ReportList"
@using Intra2025.Models
@using Intra2025.Components.Base
@using Intra2025.Data
@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.EntityFrameworkCore;
@using static Intra2025.Components.Base.JsonDataLoader;
@inject YCRSDbContext _context
@inherits BasePageComponent

<title>案件統計</title>

<style>
    .table-warning th {
        background-color: yellow !important;
        /* 確保黃底 */
        color: black !important;
        /* 確保黑字 */
    }
</style>

<div class="d-flex mb-3">
    <div class="me-3">
        <label class="form-label">案件起始日期：</label>
        <InputDate @bind-Value="StartDate" class="form-control" />
    </div>
    <div class="me-3">
        <label class="form-label">案件結束日期：</label>
        <InputDate @bind-Value="EndDate" class="form-control" />
    </div>
    <div class="align-self-end">
        <button class="btn btn-primary" @onclick="ApplyFilters">篩選</button>
    </div>
</div>


@{
    totalA = 0;
}
<table class="table table-bordered">
    <thead>
        <tr class="table-warning" style="color:black !important">
            <th>守護寶貝即時通</th>
            @foreach (var location in caseBelongs)
            {
                <th>@location.Name.Substring(0, 3)</th>
            }
            <th>合計</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var categoryA in categoriesA)
        {
            <tr>
                <td>@categoryA.Name</td>
                @foreach (var location in caseBelongs)
                {
                    <td>@GetChildRecordCount(categoryA.Id, location.Name)</td>
                }
                <td>@GetChildTotalCount(categoryA.Id)</td>
                @{
                    totalA = totalA + GetChildTotalCount(categoryA.Id);
                }
            </tr>
        }
        <tr>
            <td colspan="14"><span>(總案件數:@totalA)</span></td>
        </tr>
    </tbody>
</table>
<hr />


@{
    totalB = 0;
}
<table class="table table-bordered">
    <thead>
        <tr class="table-warning">
            <th>溫馨關懷表</th>
            @foreach (var location in caseBelongs)
            {
                <th>@location.Name.Substring(0, 3)</th>
            }
            <th>合計</th>
        </tr>
    </thead>
    <tbody>
        @foreach (var categoryB in categoriesB)
        {
            <tr>
                <td>@categoryB.Name</td>
                @foreach (var location in caseBelongs)
                {
                    <td>@GetCareRecordCount(categoryB.Id, location.Name)</td>
                }
                <td>@GetCareTotalCount(categoryB.Id)</td>
                @{
                    totalB = totalB + GetCareTotalCount(categoryB.Id);
                }
            </tr>
        }
        <tr>
            <td colspan="14"><span>(總案件數:@totalB)</span></td>
        </tr>
    </tbody>
</table>

@code {
    private List<Category> categoriesA = new();
    private List<Category> categoriesB = new();
    private List<YCRS_CareRecord> CareRecords = new();
    private List<YCRS_ChildRecord> ChildRecords = new();
    private List<CaseBelong> caseBelongs = new();
    private DateTime? StartDate { get; set; }
    private DateTime? EndDate { get; set; }
    private int totalA = 0;
    private int totalB = 0;

    protected override async Task OnInitializedAsync()
    {
        if (!_userState.IsAdmin)
        {
            NavigationManager.NavigateTo("/ChildCaseList");
            return;
        }

        var jsonDataLoader = new JsonDataLoader(env);

        // 從 JSON 載入類別
        categoriesA = jsonDataLoader.LoadCategoriesFromJson("A");
        categoriesB = jsonDataLoader.LoadCategoriesFromJson("B");
        // 從 JSON 載入戶所名稱
        caseBelongs = jsonDataLoader.LoadCaseBelongFromJson()
        .Where(cb => !cb.Name.Contains("宜蘭縣政府") && !cb.Name.Contains("計畫處") && !cb.Name.Contains("民政處"))
        .ToList();

        // 初始載入資料
        await LoadData();
    }

    private async Task LoadData()
    {
        var childQuery = _context.YCRS_ChildRecord.AsQueryable();
        var careQuery = _context.YCRS_CareRecord.AsQueryable();

        if (StartDate.HasValue)
        {
            childQuery = childQuery.Where(r => r.BelongDate >= StartDate.Value);
            careQuery = careQuery.Where(r => r.BelongDate >= StartDate.Value);
        }

        if (EndDate.HasValue)
        {
            childQuery = childQuery.Where(r => r.BelongDate <= EndDate.Value);
            careQuery = careQuery.Where(r => r.BelongDate <= EndDate.Value);
        }

        ChildRecords = await childQuery.OrderByDescending(record => record.Id).ToListAsync();
        CareRecords = await careQuery.OrderByDescending(record => record.Id).ToListAsync();
    }

    private async Task ApplyFilters()
    {
        await LoadData();
        StateHasChanged(); // 觸發 UI 重新渲染
    }

    // 根據分類 ID 及機關名稱計算數量
    private int GetChildRecordCount(string categoryId, string location)
    {
        return ChildRecords
        .Where(record => record.AttributesId == categoryId && GetLocationName(record.CaseBelongId) == location)
        .Count();
    }

    // 計算單一類別的總數量
    private int GetChildTotalCount(string categoryId)
    {
        return ChildRecords
        .Where(record => record.AttributesId == categoryId)
        .Count();
    }

    // 根據分類 ID 及機關名稱計算數量
    private int GetCareRecordCount(string categoryId, string location)
    {
        return CareRecords
        .Where(record => record.AttributesId == categoryId && GetLocationName(record.CaseBelongId) == location)
        .Count();
    }

    // 計算單一類別的總數量
    private int GetCareTotalCount(string categoryId)
    {
        return CareRecords
        .Where(record => record.AttributesId == categoryId)
        .Count();
    }

    // 透過 JSON 機關資料獲取對應的機關名稱
    private string GetLocationName(string caseBelongId)
    {
        var caseBelong = caseBelongs.FirstOrDefault(cb => cb.Id == caseBelongId);
        return caseBelong?.Name ?? "未知";
    }
}
