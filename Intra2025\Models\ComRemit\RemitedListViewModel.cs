using System;

namespace Intra2025.Models.ComRemit
{
    public class RemitedListViewModel
    {
        public int? ConSno { get; set; }
        public int Count { get; set; }
        public decimal TotalAmount { get; set; }
        public string? RemitMemo { get; set; }
        public DateTime? ConDate { get; set; }
        public string? ConMemo { get; set; }
        public DateTime? CashDate { get; set; }
        public List<RemitedListDetailViewModel> Details { get; set; } = new List<RemitedListDetailViewModel>();
    }
}
